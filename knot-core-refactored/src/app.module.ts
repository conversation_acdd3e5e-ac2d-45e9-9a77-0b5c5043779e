import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
import { BullModule } from '@nestjs/bull';
import { ThrottlerModule } from '@nestjs/throttler';
import { ScheduleModule } from '@nestjs/schedule';
import { TerminusModule } from '@nestjs/terminus';
import { LoggerModule } from 'nestjs-pino';

import { DatabaseModule } from '@infrastructure/database/database.module';
import { RedisModule } from '@infrastructure/redis/redis.module';
import { EmailModule } from '@infrastructure/email/email.module';
import { AuthModule } from '@presentation/auth/auth.module';
import { ProductModule } from '@presentation/product/product.module';
import { OrderModule } from '@presentation/order/order.module';
import { PlatformModule } from '@presentation/platform/platform.module';
import { HealthModule } from '@presentation/health/health.module';
import { SharedModule } from '@shared/shared.module';

import { appConfig } from '@shared/config/app.config';
import { databaseConfig } from '@shared/config/database.config';
import { redisConfig } from '@shared/config/redis.config';
import { queueConfig } from '@shared/config/queue.config';
import { throttlerConfig } from '@shared/config/throttler.config';
import { loggerConfig } from '@shared/config/logger.config';
import { emailConfig } from '@shared/config/email.config';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      load: [appConfig, databaseConfig, redisConfig, queueConfig, throttlerConfig, emailConfig],
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // Logging - temporarily disabled
    // LoggerModule.forRootAsync({
    //   useFactory: loggerConfig,
    // }),

    // Database
    DatabaseModule,
    TypeOrmModule.forRootAsync({
      useFactory: databaseConfig,
    }),

    // Email
    EmailModule,

    // Cache - temporarily disabled due to connection issues
    // RedisModule,
    // CacheModule.registerAsync({
    //   useFactory: redisConfig,
    // }),

    // Queue - temporarily disabled
    // BullModule.forRootAsync({
    //   useFactory: queueConfig,
    // }),

    // Rate limiting - temporarily disabled
    // ThrottlerModule.forRootAsync({
    //   useFactory: throttlerConfig,
    // }),

    // Scheduling
    ScheduleModule.forRoot(),

    // Health checks
    TerminusModule,

    // Shared utilities
    SharedModule,

    // Feature modules
    AuthModule,
    ProductModule,
    OrderModule,
    PlatformModule,
    HealthModule,
  ],
})
export class AppModule {}
