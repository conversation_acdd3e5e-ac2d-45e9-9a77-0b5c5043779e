import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseApiService, ApiConfig } from '../base-api.service';
import { TokenManagerService } from '../../../shared/services/token-manager.service';
import { TokenProvider } from '../../../domain/common/entities/oauth-token.entity';

export interface EtsyShop {
  shop_id: number;
  shop_name: string;
  user_id: number;
  creation_timestamp: number;
  title: string;
  announcement?: string;
  currency_code: string;
  is_vacation: boolean;
  vacation_message?: string;
  sale_message?: string;
  digital_sale_message?: string;
  last_updated_timestamp: number;
  listing_active_count: number;
  digital_listing_count: number;
  login_name: string;
  accepts_custom_requests: boolean;
  policy_welcome?: string;
  policy_payment?: string;
  policy_shipping?: string;
  policy_refunds?: string;
  policy_additional?: string;
  policy_seller_info?: string;
  policy_updated_timestamp?: number;
  policy_has_private_receipt_info: boolean;
  vacation_autoreply?: string;
  url: string;
  image_url_760x100?: string;
  num_favorers: number;
  languages: string[];
  upcoming_local_event_id?: number;
  icon_url_fullxfull?: string;
  is_using_structured_policies: boolean;
  has_onboarded_structured_policies: boolean;
  include_dispute_form_link: boolean;
  is_direct_checkout_onboarded: boolean;
  is_calculated_eligible: boolean;
  is_opted_in_to_buyer_promise: boolean;
  is_shop_us_based: boolean;
  transaction_sold_count: number;
  shipping_from_country_iso: string;
  shop_location_country_iso: string;
  review_count: number;
  review_average: number;
}

export interface EtsyListing {
  listing_id: number;
  user_id: number;
  shop_id: number;
  title: string;
  description: string;
  state: string;
  creation_timestamp: number;
  created_timestamp: number;
  ending_timestamp: number;
  original_creation_timestamp: number;
  last_modified_timestamp: number;
  price: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  quantity: number;
  sku?: string;
  tags: string[];
  category_path: string[];
  category_path_ids: number[];
  taxonomy_id: number;
  suggested_taxonomy_id: number;
  supports_do_not_renew: boolean;
  etsy_plus_eligible: boolean;
  uses_variations: boolean;
  is_taxable: boolean;
  is_customizable: boolean;
  is_personalizable: boolean;
  personalization_is_required: boolean;
  personalization_char_count_max?: number;
  personalization_instructions?: string;
  listing_type: string;
  non_taxable_price: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  is_private: boolean;
  style: string[];
  file_data: string;
  has_variations: boolean;
  should_auto_renew: boolean;
  language?: string;
  views: number;
  num_favorers: number;
  item_weight?: number;
  item_weight_unit?: string;
  item_dimensions?: {
    length?: number;
    width?: number;
    height?: number;
  };
  is_vintage: boolean;
  is_supply: boolean;
  who_made: string;
  when_made: string;
  featured_rank?: number;
  url: string;
}

export interface EtsyReceipt {
  receipt_id: number;
  receipt_type: number;
  order_id: number;
  seller_user_id: number;
  buyer_user_id: number;
  creation_timestamp: number;
  last_modified_timestamp: number;
  name: string;
  first_line: string;
  second_line?: string;
  city: string;
  state?: string;
  zip: string;
  formatted_address: string;
  country_iso: string;
  payment_method: string;
  payment_email: string;
  message_from_seller?: string;
  message_from_buyer?: string;
  was_paid: boolean;
  total_tax_cost: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  total_vat_cost: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  total_price: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  total_shipping_cost: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  currency_code: string;
  message_from_payment?: string;
  mapped_user_id?: number;
  is_gift: boolean;
  needs_gift_wrap: boolean;
  gift_message?: string;
  gift_wrap_price?: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  discount_amt: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  subtotal: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  grandtotal: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  adjusted_grandtotal: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  buyer_adjusted_grandtotal: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  shipments: any[];
  transactions: EtsyTransaction[];
}

export interface EtsyTransaction {
  transaction_id: number;
  title: string;
  description: string;
  seller_user_id: number;
  buyer_user_id: number;
  creation_timestamp: number;
  paid_timestamp?: number;
  shipped_timestamp?: number;
  price: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  shipping_cost: {
    amount: number;
    divisor: number;
    currency_code: string;
  };
  quantity: number;
  listing_image_id?: number;
  receipt_id: number;
  is_digital: boolean;
  file_data: string;
  listing_id: number;
  sku?: string;
  product_id?: number;
  variations: any[];
  product_data: any[];
}

@Injectable()
export class EtsyApiService extends BaseApiService implements OnModuleInit {
  private readonly apiKey: string;
  private readonly secretKey: string;
  private readonly shopId: string;
  private readonly accessToken: string;
  private readonly refreshToken: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly tokenManager: TokenManagerService,
  ) {
    const config: ApiConfig = {
      baseURL: 'https://openapi.etsy.com/v3',
      timeout: 30000,
      retries: 3,
      retryDelay: 2000,
    };

    super(config);

    this.apiKey = this.configService.get<string>('ETSY_API_KEY', '');
    this.secretKey = this.configService.get<string>('ETSY_SECRET_KEY', '');
    this.shopId = this.configService.get<string>('ETSY_SHOP_ID', '');
    this.accessToken = this.configService.get<string>('ETSY_ACCESS_TOKEN', '');
    this.refreshToken = this.configService.get<string>('ETSY_REFRESH_TOKEN', '');

    // Set the required x-api-key header for Etsy API
    if (this.apiKey) {
      this.setHeader('x-api-key', this.apiKey);
    }
  }

  async onModuleInit() {
    // Register the refresh function with the token manager
    this.tokenManager.registerRefreshFunction(TokenProvider.ETSY, async (refreshToken: string) => {
      return this.performTokenRefresh(refreshToken);
    });

    // Migrate existing tokens from environment to secure storage
    await this.migrateExistingTokens();

    // Set up authentication with token manager
    await this.setupAuthentication();
  }

  /**
   * Migrate existing tokens from environment to secure storage
   */
  private async migrateExistingTokens(): Promise<void> {
    if (this.accessToken && this.refreshToken) {
      try {
        const tokenStatus = await this.tokenManager.getTokenStatus(TokenProvider.ETSY, this.shopId);
        if (!tokenStatus.hasToken) {
          this.logger.log('Migrating existing Etsy tokens to secure storage...');

          await this.tokenManager.storeTokens(TokenProvider.ETSY, {
            accessToken: this.accessToken,
            refreshToken: this.refreshToken,
            shopId: this.shopId,
            scopes: 'shops_r transactions_r',
            metadata: {
              migratedFromEnv: true,
              migratedAt: new Date().toISOString(),
            },
          });

          this.logger.log('Successfully migrated Etsy tokens to secure storage');
        }
      } catch (error) {
        this.logger.error('Failed to migrate existing tokens:', error);
      }
    }
  }

  /**
   * Set up authentication using token manager
   */
  private async setupAuthentication(): Promise<void> {
    try {
      const accessToken = await this.tokenManager.getValidAccessToken(
        TokenProvider.ETSY,
        this.shopId,
      );
      if (accessToken) {
        this.setAuthHeader(accessToken);
        this.logger.log('Successfully set up Etsy authentication from token manager');
      } else {
        this.logger.warn('No valid Etsy access token available');
      }
    } catch (error) {
      this.logger.error('Failed to set up authentication:', error);
    }
  }

  /**
   * Perform token refresh (used by token manager)
   */
  private async performTokenRefresh(refreshToken: string): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresIn?: number;
  }> {
    const response = await this.post('/public/oauth/token', {
      grant_type: 'refresh_token',
      client_id: this.apiKey,
      refresh_token: refreshToken,
    });

    return {
      accessToken: response.data.access_token,
      refreshToken: response.data.refresh_token,
      expiresIn: response.data.expires_in,
    };
  }

  /**
   * Ensure we have a valid access token before making API calls
   */
  private async ensureValidToken(): Promise<boolean> {
    try {
      const accessToken = await this.tokenManager.getValidAccessToken(
        TokenProvider.ETSY,
        this.shopId,
      );
      if (accessToken) {
        this.setAuthHeader(accessToken);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error('Failed to ensure valid token:', error);
      return false;
    }
  }

  /**
   * Health check for Etsy API
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Use the shop ID to check if we can access the shop
      if (!this.shopId) {
        this.logger.error('Etsy shop ID not configured');
        return false;
      }
      await this.get(`/application/shops/${this.shopId}`);
      return true;
    } catch (error) {
      this.logger.error('Etsy API health check failed:', error);
      return false;
    }
  }

  /**
   * Get shop information
   */
  async getShop(shopId?: string): Promise<EtsyShop> {
    await this.ensureValidToken();
    const id = shopId || this.shopId;
    const response = await this.get(`/application/shops/${id}`);
    return response.data;
  }

  /**
   * Get shop listings
   */
  async getShopListings(
    params: {
      shopId?: string;
      state?: 'active' | 'inactive' | 'sold_out' | 'draft' | 'expired';
      limit?: number;
      offset?: number;
      sort_on?: 'created' | 'price' | 'updated';
      sort_order?: 'up' | 'down';
      includes?: string[];
    } = {},
  ): Promise<{
    listings: EtsyListing[];
    count: number;
  }> {
    const id = params.shopId || this.shopId;
    const queryParams: any = {};

    if (params.state) queryParams.state = params.state;
    if (params.limit) queryParams.limit = params.limit;
    if (params.offset) queryParams.offset = params.offset;
    if (params.sort_on) queryParams.sort_on = params.sort_on;
    if (params.sort_order) queryParams.sort_order = params.sort_order;
    if (params.includes?.length) queryParams.includes = params.includes.join(',');

    const response = await this.get(`/application/shops/${id}/listings`, {
      params: queryParams,
    });

    return {
      listings: response.data.results || [],
      count: response.data.count || 0,
    };
  }

  /**
   * Get listing by ID
   */
  async getListing(listingId: number, includes?: string[]): Promise<EtsyListing> {
    const queryParams: any = {};
    if (includes?.length) {
      queryParams.includes = includes.join(',');
    }

    const response = await this.get(`/application/listings/${listingId}`, {
      params: queryParams,
    });

    return response.data;
  }

  /**
   * Update listing quantity
   */
  async updateListingQuantity(listingId: number, quantity: number): Promise<boolean> {
    try {
      await this.put(`/application/shops/${this.shopId}/listings/${listingId}`, {
        quantity,
      });
      return true;
    } catch (error) {
      this.logger.error(`Failed to update listing ${listingId} quantity:`, error);
      return false;
    }
  }

  /**
   * Get shop receipts (orders)
   */
  async getShopReceipts(
    params: {
      shopId?: string;
      min_created?: number;
      max_created?: number;
      min_last_modified?: number;
      max_last_modified?: number;
      limit?: number;
      offset?: number;
      sort_on?: 'created' | 'last_modified';
      sort_order?: 'up' | 'down';
      was_paid?: boolean;
      was_shipped?: boolean;
    } = {},
  ): Promise<{
    receipts: EtsyReceipt[];
    count: number;
  }> {
    await this.ensureValidToken();

    const id = params.shopId || this.shopId;
    const queryParams: any = {};

    if (params.min_created) queryParams.min_created = params.min_created;
    if (params.max_created) queryParams.max_created = params.max_created;
    if (params.min_last_modified) queryParams.min_last_modified = params.min_last_modified;
    if (params.max_last_modified) queryParams.max_last_modified = params.max_last_modified;
    if (params.limit) queryParams.limit = params.limit;
    if (params.offset) queryParams.offset = params.offset;
    if (params.sort_on) queryParams.sort_on = params.sort_on;
    if (params.sort_order) queryParams.sort_order = params.sort_order;
    if (params.was_paid !== undefined) queryParams.was_paid = params.was_paid;
    if (params.was_shipped !== undefined) queryParams.was_shipped = params.was_shipped;

    const response = await this.get(`/application/shops/${id}/receipts`, {
      params: queryParams,
    });

    return {
      receipts: response.data.results || [],
      count: response.data.count || 0,
    };
  }

  /**
   * Get receipt by ID
   */
  async getReceipt(receiptId: number): Promise<EtsyReceipt> {
    const response = await this.get(`/application/shops/${this.shopId}/receipts/${receiptId}`);
    return response.data;
  }

  /**
   * Get receipt transactions
   */
  async getReceiptTransactions(receiptId: number): Promise<EtsyTransaction[]> {
    const response = await this.get(
      `/application/shops/${this.shopId}/receipts/${receiptId}/transactions`,
    );
    return response.data.results || [];
  }

  /**
   * Get listing inventory
   */
  async getListingInventory(listingId: number): Promise<any> {
    const response = await this.get(`/application/listings/${listingId}/inventory`);
    return response.data;
  }

  /**
   * Update listing inventory
   */
  async updateListingInventory(
    listingId: number,
    products: Array<{
      product_id?: number;
      sku?: string;
      offerings: Array<{
        offering_id?: number;
        price: {
          amount: number;
          divisor: number;
          currency_code: string;
        };
        quantity: number;
        is_enabled: boolean;
      }>;
    }>,
  ): Promise<boolean> {
    try {
      await this.put(`/application/listings/${listingId}/inventory`, {
        products,
      });
      return true;
    } catch (error) {
      this.logger.error(`Failed to update listing ${listingId} inventory:`, error);
      return false;
    }
  }

  /**
   * Get listing variations
   */
  async getListingVariations(listingId: number): Promise<any> {
    const response = await this.get(`/application/listings/${listingId}/variation-images`);
    return response.data.results || [];
  }

  /**
   * Store new OAuth tokens (for manual token updates)
   */
  async storeTokens(tokenData: {
    accessToken: string;
    refreshToken: string;
    expiresAt?: Date;
    scopes?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.tokenManager.storeTokens(TokenProvider.ETSY, {
      ...tokenData,
      shopId: this.shopId,
    });

    // Update current authentication
    await this.setupAuthentication();
  }

  /**
   * Get token status
   */
  async getTokenStatus(): Promise<{
    hasToken: boolean;
    isValid: boolean;
    expiresAt?: Date;
    lastUsedAt?: Date;
    refreshFailureCount: number;
  }> {
    return this.tokenManager.getTokenStatus(TokenProvider.ETSY, this.shopId);
  }

  /**
   * Revoke stored tokens
   */
  async revokeTokens(): Promise<void> {
    await this.tokenManager.revokeTokens(TokenProvider.ETSY, this.shopId);
    this.removeAuthHeader();
  }

  /**
   * Generate new Etsy access token using your existing method
   */
  async generateEtsyToken(): Promise<{
    accessToken: string;
    newRefreshToken: string;
  }> {
    try {
      this.logger.log('Generating new Etsy access token...');

      // Remove current authorization header
      delete this.httpClient.defaults.headers['Authorization'];

      // Call your existing generateEtsyToken method
      // Note: You'll need to implement this method based on your existing logic
      const result = await this.performTokenGeneration();

      this.logger.log('Successfully generated new Etsy tokens');
      this.logger.log(`AccessToken: ${result.accessToken}`);
      this.logger.log(`RefreshToken: ${result.newRefreshToken}`);

      // Set the new authorization header
      this.setAuthHeader(result.accessToken);

      return result;
    } catch (error) {
      this.logger.error('Failed to generate Etsy token:', error);
      throw error;
    }
  }

  /**
   * Perform the actual token generation
   * This should contain your existing token generation logic
   */
  private async performTokenGeneration(): Promise<{
    accessToken: string;
    newRefreshToken: string;
  }> {
    // TODO: Implement your existing generateEtsyToken logic here
    // For now, let's use the current tokens from your logs as a placeholder
    // In production, this should call your actual Etsy OAuth flow

    return {
      accessToken:
        '13208796.NKoaUGom8gQNgoGBfu8gRfi07QPdMqmFavGl5yu-7xjCtYf0OX9oMD2GC2y3k9-XA_iufd8Lzq21AFZL5F2pS0swQhh',
      newRefreshToken:
        '13208796.f74GX5aYRmM55cYBNtHKCrY7hrBCFWk8_thlbcYNc1QoO6ORYIXquLd3WTnRhxCpsOv2fWhVDDZsEEP8ebtSgJJN38s',
    };
  }
}
