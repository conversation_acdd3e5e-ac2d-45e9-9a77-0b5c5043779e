import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseApiService, ApiConfig } from '../base-api.service';

export interface ShopifyProduct {
  id: number | null;
  title: string;
  body_html: string;
  vendor: string;
  product_type: string;
  created_at: string;
  handle: string;
  updated_at: string;
  published_at: string;
  template_suffix?: string;
  status: string;
  published_scope: string;
  tags: string;
  admin_graphql_api_id: string;
  variants: ShopifyVariant[];
  options: ShopifyOption[];
  images: ShopifyImage[];
  image?: ShopifyImage;
}

export interface ShopifyVariant {
  id: number;
  product_id: number;
  title: string;
  price: string;
  sku: string;
  position: number;
  inventory_policy: string;
  compare_at_price?: string;
  fulfillment_service: string;
  inventory_management: string;
  option1?: string;
  option2?: string;
  option3?: string;
  created_at: string;
  updated_at: string;
  taxable: boolean;
  barcode?: string;
  grams: number;
  image_id?: number;
  weight: number;
  weight_unit: string;
  inventory_item_id: number | null;
  inventory_quantity: number | null;
  old_inventory_quantity: number;
  requires_shipping: boolean;
  admin_graphql_api_id: string;
}

export interface ShopifyOption {
  id: number;
  product_id: number;
  name: string;
  position: number;
  values: string[];
}

export interface ShopifyImage {
  id: number;
  product_id: number;
  position: number;
  created_at: string;
  updated_at: string;
  alt?: string;
  width: number;
  height: number;
  src: string;
  variant_ids: number[];
  admin_graphql_api_id: string;
}

export interface ShopifyOrder {
  id: number | null;
  admin_graphql_api_id: string;
  app_id?: number;
  browser_ip?: string;
  buyer_accepts_marketing: boolean;
  cancel_reason?: string;
  cancelled_at?: string;
  cart_token?: string;
  checkout_id?: number;
  checkout_token?: string;
  client_details?: any;
  closed_at?: string;
  confirmed: boolean;
  contact_email?: string;
  created_at: string;
  currency: string;
  current_subtotal_price: string;
  current_subtotal_price_set: any;
  current_total_discounts: string;
  current_total_discounts_set: any;
  current_total_duties_set?: any;
  current_total_price: string;
  current_total_price_set: any;
  current_total_tax: string;
  current_total_tax_set: any;
  customer_locale?: string;
  device_id?: number;
  discount_codes: any[];
  email: string;
  estimated_taxes: boolean;
  financial_status: string;
  fulfillment_status?: string;
  gateway?: string;
  landing_site?: string;
  landing_site_ref?: string;
  location_id?: number;
  name: string;
  note?: string;
  note_attributes: any[];
  number: number;
  order_number: number;
  order_status_url: string;
  original_total_duties_set?: any;
  payment_gateway_names: string[];
  phone?: string;
  presentment_currency: string;
  processed_at: string;
  processing_method: string;
  reference?: string;
  referring_site?: string;
  source_identifier?: string;
  source_name: string;
  source_url?: string;
  subtotal_price: string;
  subtotal_price_set: any;
  tags: string;
  tax_lines: any[];
  taxes_included: boolean;
  test: boolean;
  token: string;
  total_discounts: string;
  total_discounts_set: any;
  total_line_items_price: string;
  total_line_items_price_set: any;
  total_outstanding: string;
  total_price: string;
  total_price_set: any;
  total_price_usd: string;
  total_shipping_price_set: any;
  total_tax: string;
  total_tax_set: any;
  total_tip_received: string;
  total_weight: number;
  updated_at: string;
  user_id?: number;
  billing_address?: ShopifyAddress;
  customer?: ShopifyCustomer;
  discount_applications: any[];
  fulfillments: any[];
  line_items: ShopifyLineItem[];
  payment_terms?: any;
  refunds: any[];
  shipping_address?: ShopifyAddress;
  shipping_lines: any[];
}

export interface ShopifyLineItem {
  id: number;
  admin_graphql_api_id: string;
  fulfillable_quantity: number;
  fulfillment_service: string;
  fulfillment_status?: string;
  gift_card: boolean;
  grams: number;
  name: string;
  origin_location?: any;
  price: string;
  price_set: any;
  product_exists: boolean;
  product_id: number | null;
  properties: any[];
  quantity: number;
  requires_shipping: boolean;
  sku: string;
  taxable: boolean;
  title: string;
  total_discount: string;
  total_discount_set: any;
  variant_id: number | null;
  variant_inventory_management: string;
  variant_title?: string;
  vendor: string;
  tax_lines: any[];
  duties: any[];
  discount_allocations: any[];
}

export interface ShopifyAddress {
  first_name?: string;
  address1?: string;
  phone?: string;
  city?: string;
  zip?: string;
  province?: string;
  country?: string;
  last_name?: string;
  address2?: string;
  company?: string;
  latitude?: number;
  longitude?: number;
  name?: string;
  country_code?: string;
  province_code?: string;
}

export interface ShopifyCustomer {
  id: number;
  email: string;
  accepts_marketing: boolean;
  created_at: string;
  updated_at: string;
  first_name: string;
  last_name: string;
  orders_count: number;
  state: string;
  total_spent: string;
  last_order_id?: number;
  note?: string;
  verified_email: boolean;
  multipass_identifier?: string;
  tax_exempt: boolean;
  phone?: string;
  tags: string;
  last_order_name?: string;
  currency: string;
  addresses: ShopifyAddress[];
  accepts_marketing_updated_at: string;
  marketing_opt_in_level?: string;
  tax_exemptions: any[];
  admin_graphql_api_id: string;
  default_address?: ShopifyAddress;
}

export interface ShopifyInventoryLevel {
  inventory_item_id: number;
  location_id: number;
  available: number;
  updated_at: string;
  admin_graphql_api_id: string;
}

@Injectable()
export class ShopifyApiService extends BaseApiService {
  private readonly shopDomain: string;
  private readonly accessToken: string;
  private readonly apiVersion: string;

  constructor(private readonly configService: ConfigService) {
    const shopDomain = configService.get<string>('SHOPIFY_SHOP_DOMAIN', '');
    const accessToken = configService.get<string>('SHOPIFY_ACCESS_TOKEN', '');
    const apiVersion = configService.get<string>('SHOPIFY_API_VERSION', '2023-10');

    const config: ApiConfig = {
      baseURL: `https://${shopDomain}/admin/api/${apiVersion}`,
      timeout: 30000,
      retries: 3,
      retryDelay: 2000,
      headers: {
        'X-Shopify-Access-Token': accessToken,
      },
    };

    super(config);

    this.shopDomain = shopDomain;
    this.accessToken = accessToken;
    this.apiVersion = apiVersion;
  }

  /**
   * Health check for Shopify API
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.get('/shop.json');
      return true;
    } catch (error) {
      this.logger.error('Shopify API health check failed:', error);
      return false;
    }
  }

  /**
   * Get shop information
   */
  async getShop(): Promise<any> {
    const response = await this.get('/shop.json');
    return response.data.shop;
  }

  /**
   * Get products
   */
  async getProducts(
    params: {
      ids?: string;
      limit?: number;
      since_id?: number;
      title?: string;
      vendor?: string;
      handle?: string;
      product_type?: string;
      status?: 'active' | 'archived' | 'draft';
      created_at_min?: string;
      created_at_max?: string;
      updated_at_min?: string;
      updated_at_max?: string;
      published_at_min?: string;
      published_at_max?: string;
      published_status?: 'published' | 'unpublished' | 'any';
      fields?: string;
    } = {},
  ): Promise<ShopifyProduct[]> {
    const response = await this.get('/products.json', { params });
    return response.data.products || [];
  }

  /**
   * Get product by ID
   */
  async getProduct(productId: number, fields?: string): Promise<ShopifyProduct> {
    const params = fields ? { fields } : {};
    const response = await this.get(`/products/${productId}.json`, { params });
    return response.data.product;
  }

  /**
   * Update product
   */
  async updateProduct(
    productId: number,
    product: Partial<ShopifyProduct>,
  ): Promise<ShopifyProduct> {
    const response = await this.put(`/products/${productId}.json`, { product });
    return response.data.product;
  }

  /**
   * Get product variants
   */
  async getProductVariants(productId: number): Promise<ShopifyVariant[]> {
    const response = await this.get(`/products/${productId}/variants.json`);
    return response.data.variants || [];
  }

  /**
   * Get variant by ID
   */
  async getVariant(variantId: number): Promise<ShopifyVariant> {
    const response = await this.get(`/variants/${variantId}.json`);
    return response.data.variant;
  }

  /**
   * Update variant
   */
  async updateVariant(
    variantId: number,
    variant: Partial<ShopifyVariant>,
  ): Promise<ShopifyVariant> {
    const response = await this.put(`/variants/${variantId}.json`, { variant });
    return response.data.variant;
  }

  /**
   * Get orders
   */
  async getOrders(
    params: {
      ids?: string;
      limit?: number;
      since_id?: number;
      created_at_min?: string;
      created_at_max?: string;
      updated_at_min?: string;
      updated_at_max?: string;
      processed_at_min?: string;
      processed_at_max?: string;
      attribution_app_id?: string;
      status?: 'open' | 'closed' | 'cancelled' | 'any';
      financial_status?:
        | 'authorized'
        | 'pending'
        | 'paid'
        | 'partially_paid'
        | 'refunded'
        | 'voided'
        | 'partially_refunded'
        | 'any'
        | 'unpaid';
      fulfillment_status?: 'shipped' | 'partial' | 'unshipped' | 'any' | 'unfulfilled';
      fields?: string;
    } = {},
  ): Promise<ShopifyOrder[]> {
    const response = await this.get('/orders.json', { params });
    return response.data.orders || [];
  }

  /**
   * Get order by ID
   */
  async getOrder(orderId: number, fields?: string): Promise<ShopifyOrder> {
    const params = fields ? { fields } : {};
    const response = await this.get(`/orders/${orderId}.json`, { params });
    return response.data.order;
  }

  /**
   * Get inventory levels
   */
  async getInventoryLevels(params: {
    inventory_item_ids?: string;
    location_ids?: string;
    limit?: number;
    updated_at_min?: string;
  }): Promise<ShopifyInventoryLevel[]> {
    const response = await this.get('/inventory_levels.json', { params });
    return response.data.inventory_levels || [];
  }

  /**
   * Update inventory level
   */
  async updateInventoryLevel(
    inventoryItemId: number,
    locationId: number,
    available: number,
  ): Promise<ShopifyInventoryLevel> {
    const response = await this.post('/inventory_levels/set.json', {
      inventory_item_id: inventoryItemId,
      location_id: locationId,
      available,
    });
    return response.data.inventory_level;
  }

  /**
   * Adjust inventory level
   */
  async adjustInventoryLevel(
    inventoryItemId: number,
    locationId: number,
    quantity: number,
  ): Promise<ShopifyInventoryLevel> {
    const response = await this.post('/inventory_levels/adjust.json', {
      inventory_item_id: inventoryItemId,
      location_id: locationId,
      quantity,
    });
    return response.data.inventory_level;
  }

  /**
   * Get locations
   */
  async getLocations(): Promise<any[]> {
    const response = await this.get('/locations.json');
    return response.data.locations || [];
  }

  /**
   * Get inventory items
   */
  async getInventoryItems(
    params: {
      ids?: string;
      limit?: number;
    } = {},
  ): Promise<any[]> {
    const response = await this.get('/inventory_items.json', { params });
    return response.data.inventory_items || [];
  }

  /**
   * Get inventory item by ID
   */
  async getInventoryItem(inventoryItemId: number): Promise<any> {
    const response = await this.get(`/inventory_items/${inventoryItemId}.json`);
    return response.data.inventory_item;
  }

  /**
   * Update inventory item
   */
  async updateInventoryItem(inventoryItemId: number, inventoryItem: any): Promise<any> {
    const response = await this.put(`/inventory_items/${inventoryItemId}.json`, {
      inventory_item: inventoryItem,
    });
    return response.data.inventory_item;
  }

  /**
   * Get webhooks
   */
  async getWebhooks(): Promise<any[]> {
    const response = await this.get('/webhooks.json');
    return response.data.webhooks || [];
  }

  /**
   * Create webhook
   */
  async createWebhook(webhook: {
    topic: string;
    address: string;
    format?: 'json' | 'xml';
    fields?: string[];
  }): Promise<any> {
    const response = await this.post('/webhooks.json', { webhook });
    return response.data.webhook;
  }

  /**
   * Delete webhook
   */
  async deleteWebhook(webhookId: number): Promise<boolean> {
    try {
      await this.delete(`/webhooks/${webhookId}.json`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete webhook ${webhookId}:`, error);
      return false;
    }
  }

  /**
   * Set access token
   */
  setAccessToken(token: string): void {
    this.setHeader('X-Shopify-Access-Token', token);
  }

  /**
   * Remove access token
   */
  removeAccessToken(): void {
    this.removeHeader('X-Shopify-Access-Token');
  }
}
