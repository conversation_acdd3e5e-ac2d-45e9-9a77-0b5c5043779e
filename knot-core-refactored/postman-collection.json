{"info": {"name": "Knot Core API - Complete Collection", "description": "Complete Postman collection for Knot Core API including Shopify & Etsy sync endpoints", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3002/api/v1", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}], "item": [{"name": "🔐 Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.accessToken) {", "        pm.collectionVariables.set('jwt_token', response.data.accessToken);", "        console.log('JWT token saved to collection variable');", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\"\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/profile", "host": ["{{base_url}}"], "path": ["auth", "profile"]}}}]}, {"name": "📊 Platform Status & Health", "item": [{"name": "Platform Status (All)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/platform/status", "host": ["{{base_url}}"], "path": ["platform", "status"]}}}, {"name": "Shopify Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/platform/status/shopify", "host": ["{{base_url}}"], "path": ["platform", "status", "shopify"]}}}, {"name": "Etsy Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/platform/status/etsy", "host": ["{{base_url}}"], "path": ["platform", "status", "etsy"]}}}, {"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}}]}, {"name": "🛒 Shopify Sync", "item": [{"name": "Sync ALL Shopify Orders (with Pagination)", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/platform/sync/orders/shopify?startDate=2020-01-01T00:00:00Z&endDate=2025-12-31T23:59:59Z&limit=10000&forceUpdate=false", "host": ["{{base_url}}"], "path": ["platform", "sync", "orders", "shopify"], "query": [{"key": "startDate", "value": "2020-01-01T00:00:00Z", "description": "Start date for order sync (ISO 8601 format)"}, {"key": "endDate", "value": "2025-12-31T23:59:59Z", "description": "End date for order sync (ISO 8601 format)"}, {"key": "limit", "value": "10000", "description": "Maximum number of orders to sync (now supports pagination!)"}, {"key": "forceUpdate", "value": "false", "description": "Force update existing orders"}]}}}, {"name": "Sync Last Month Shopify Orders", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/platform/sync/orders/shopify?startDate=2024-07-01T00:00:00Z&endDate=2024-07-31T23:59:59Z&limit=2000&forceUpdate=false", "host": ["{{base_url}}"], "path": ["platform", "sync", "orders", "shopify"], "query": [{"key": "startDate", "value": "2024-07-01T00:00:00Z", "description": "Start of last month"}, {"key": "endDate", "value": "2024-07-31T23:59:59Z", "description": "End of last month"}, {"key": "limit", "value": "2000", "description": "High limit to test pagination"}, {"key": "forceUpdate", "value": "false", "description": "Force update existing orders"}]}}}, {"name": "Sync Last Week Shopify Orders", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/platform/sync/orders/shopify?startDate=2025-07-28T00:00:00Z&endDate=2025-08-05T23:59:59Z&limit=100&forceUpdate=false", "host": ["{{base_url}}"], "path": ["platform", "sync", "orders", "shopify"], "query": [{"key": "startDate", "value": "2025-07-28T00:00:00Z"}, {"key": "endDate", "value": "2025-08-05T23:59:59Z"}, {"key": "limit", "value": "100"}, {"key": "forceUpdate", "value": "false"}]}}}, {"name": "Sync Today's Shopify Orders", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/platform/sync/orders/shopify?startDate={{$isoTimestamp}}&endDate={{$isoTimestamp}}&limit=50&forceUpdate=false", "host": ["{{base_url}}"], "path": ["platform", "sync", "orders", "shopify"], "query": [{"key": "startDate", "value": "{{$isoTimestamp}}", "description": "Dynamic: Today's date"}, {"key": "endDate", "value": "{{$isoTimestamp}}", "description": "Dynamic: Today's date"}, {"key": "limit", "value": "50"}, {"key": "forceUpdate", "value": "false"}]}}}, {"name": "Sync Shopify Products", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/platform/sync/shopify/products", "host": ["{{base_url}}"], "path": ["platform", "sync", "shopify", "products"]}}}]}, {"name": "🎨 Etsy Sync", "item": [{"name": "Sync ALL Etsy Orders", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/platform/sync/orders/etsy?startDate=2020-01-01T00:00:00Z&endDate=2025-12-31T23:59:59Z&limit=5000&forceUpdate=false", "host": ["{{base_url}}"], "path": ["platform", "sync", "orders", "etsy"], "query": [{"key": "startDate", "value": "2020-01-01T00:00:00Z"}, {"key": "endDate", "value": "2025-12-31T23:59:59Z"}, {"key": "limit", "value": "5000"}, {"key": "forceUpdate", "value": "false"}]}}}, {"name": "Sync Last Week Etsy Orders", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/platform/sync/orders/etsy?startDate=2025-07-28T00:00:00Z&endDate=2025-08-05T23:59:59Z&limit=100&forceUpdate=false", "host": ["{{base_url}}"], "path": ["platform", "sync", "orders", "etsy"], "query": [{"key": "startDate", "value": "2025-07-28T00:00:00Z"}, {"key": "endDate", "value": "2025-08-05T23:59:59Z"}, {"key": "limit", "value": "100"}, {"key": "forceUpdate", "value": "false"}]}}}, {"name": "Sync Etsy Products", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/platform/sync/etsy/products", "host": ["{{base_url}}"], "path": ["platform", "sync", "etsy", "products"]}}}]}, {"name": "📦 Orders Management", "item": [{"name": "Get All Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/orders?limit=50&page=1&sortBy=externalCreatedAt&sortOrder=desc", "host": ["{{base_url}}"], "path": ["orders"], "query": [{"key": "limit", "value": "50", "description": "Number of orders per page"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "sortBy", "value": "externalCreatedAt", "description": "Field to sort by"}, {"key": "sortOrder", "value": "desc", "description": "Sort order (asc/desc)"}]}}}, {"name": "Get Orders Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/orders/stats", "host": ["{{base_url}}"], "path": ["orders", "stats"]}}}, {"name": "Get Recent Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/orders/recent?limit=20", "host": ["{{base_url}}"], "path": ["orders", "recent"], "query": [{"key": "limit", "value": "20"}]}}}, {"name": "Filter Orders by Platform", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/orders?source=shopify&limit=100", "host": ["{{base_url}}"], "path": ["orders"], "query": [{"key": "source", "value": "shopify", "description": "Filter by platform: shopify, etsy, amazon"}, {"key": "limit", "value": "100"}]}}}, {"name": "Filter Orders by Date Range", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/orders?startDate=2025-08-01T00:00:00Z&endDate=2025-08-05T23:59:59Z&limit=100", "host": ["{{base_url}}"], "path": ["orders"], "query": [{"key": "startDate", "value": "2025-08-01T00:00:00Z"}, {"key": "endDate", "value": "2025-08-05T23:59:59Z"}, {"key": "limit", "value": "100"}]}}}]}, {"name": "🔧 Advanced Sync Operations", "item": [{"name": "Sync All Platforms (Orders)", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/platform/sync/orders/all?startDate=2020-01-01T00:00:00Z&endDate=2025-12-31T23:59:59Z&limit=1000", "host": ["{{base_url}}"], "path": ["platform", "sync", "orders", "all"], "query": [{"key": "startDate", "value": "2020-01-01T00:00:00Z"}, {"key": "endDate", "value": "2025-12-31T23:59:59Z"}, {"key": "limit", "value": "1000"}]}}}, {"name": "Sync Last Week Orders (All Platforms)", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/platform/sync/last-week-orders", "host": ["{{base_url}}"], "path": ["platform", "sync", "last-week-orders"]}}}, {"name": "Force Update Existing Orders", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/platform/sync/orders/shopify?startDate=2025-08-01T00:00:00Z&endDate=2025-08-05T23:59:59Z&limit=100&forceUpdate=true", "host": ["{{base_url}}"], "path": ["platform", "sync", "orders", "shopify"], "query": [{"key": "startDate", "value": "2025-08-01T00:00:00Z"}, {"key": "endDate", "value": "2025-08-05T23:59:59Z"}, {"key": "limit", "value": "100"}, {"key": "forceUpdate", "value": "true", "description": "Force update existing orders"}]}}}]}, {"name": "🔑 Token Management", "item": [{"name": "Get Etsy Token Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/platform/tokens/etsy/status", "host": ["{{base_url}}"], "path": ["platform", "tokens", "etsy", "status"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/platform/tokens/etsy/refresh", "host": ["{{base_url}}"], "path": ["platform", "tokens", "etsy", "refresh"]}}}, {"name": "Get Token Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/platform/tokens/statistics", "host": ["{{base_url}}"], "path": ["platform", "tokens", "statistics"]}}}]}, {"name": "🛠️ Development & Testing", "item": [{"name": "Seed Database", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/health/seed", "host": ["{{base_url}}"], "path": ["health", "seed"]}}}, {"name": "Verify User Email", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/health/verify-user/<EMAIL>", "host": ["{{base_url}}"], "path": ["health", "verify-user", "<EMAIL>"]}}}, {"name": "Platform Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/platform/statistics", "host": ["{{base_url}}"], "path": ["platform", "statistics"]}}}, {"name": "Platform History", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/platform/history", "host": ["{{base_url}}"], "path": ["platform", "history"]}}}]}]}